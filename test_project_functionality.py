#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
项目功能测试脚本
测试清理后的项目是否正常工作
"""

import os
import sys
import pandas as pd
import traceback
from datetime import datetime

def print_section(title):
    """打印测试章节标题"""
    print(f"\n{'='*60}")
    print(f"🧪 {title}")
    print(f"{'='*60}")

def print_test_result(test_name, success, details=""):
    """打印测试结果"""
    status = "✅ 通过" if success else "❌ 失败"
    print(f"{status} {test_name}")
    if details:
        print(f"   详情: {details}")

def test_basic_imports():
    """测试基础导入"""
    print_section("基础导入测试")
    
    results = {}
    
    # 测试pandas
    try:
        import pandas as pd
        results['pandas'] = True
        print_test_result("Pandas导入", True, f"版本: {pd.__version__}")
    except Exception as e:
        results['pandas'] = False
        print_test_result("Pandas导入", False, str(e))
    
    # 测试pandasai
    try:
        import pandasai
        results['pandasai'] = True
        try:
            from pandasai.__version__ import __version__
            version = __version__
        except:
            version = "无法获取版本"
        print_test_result("PandasAI导入", True, f"版本: {version}")
    except Exception as e:
        results['pandasai'] = False
        print_test_result("PandasAI导入", False, str(e))
    
    # 测试SmartDataframe
    try:
        from pandasai import SmartDataframe
        results['smartdataframe'] = True
        print_test_result("SmartDataframe导入", True)
    except Exception as e:
        results['smartdataframe'] = False
        print_test_result("SmartDataframe导入", False, str(e))
    
    # 测试LLM基类
    try:
        from pandasai.llm.base import LLM
        results['llm_base'] = True
        print_test_result("LLM基类导入", True)
    except Exception as e:
        results['llm_base'] = False
        print_test_result("LLM基类导入", False, str(e))
    
    return results

def test_core_files_exist():
    """测试核心文件是否存在"""
    print_section("核心文件存在性测试")
    
    core_files = [
        'perfect_tongyi_integration.py',
        'working_tongyi_integration.py', 
        'tongyi_qianwen_integration.py',
        'pandasai_v2_examples.py',
        'final_verification.py',
        'working_example.py'
    ]
    
    results = {}
    for file in core_files:
        exists = os.path.exists(file)
        results[file] = exists
        print_test_result(f"文件存在: {file}", exists)
    
    return results

def test_data_creation():
    """测试数据创建功能"""
    print_section("数据创建测试")
    
    try:
        # 创建测试数据
        data = {
            '产品': ['iPhone', 'iPad', 'MacBook', 'AirPods'],
            '销量': [1000, 800, 500, 1200],
            '价格': [6999, 4599, 14999, 1899],
            '类别': ['手机', '平板', '笔记本', '配件']
        }
        
        df = pd.DataFrame(data)
        print_test_result("DataFrame创建", True, f"形状: {df.shape}")
        
        # 测试基本操作
        total_sales = df['销量'].sum()
        avg_price = df['价格'].mean()
        
        print_test_result("基本计算", True, f"总销量: {total_sales}, 平均价格: {avg_price:.2f}")
        
        return True, df
        
    except Exception as e:
        print_test_result("数据创建", False, str(e))
        return False, None

def test_tongyi_integration_import():
    """测试通义千问集成文件导入"""
    print_section("通义千问集成导入测试")
    
    results = {}
    
    # 测试perfect_tongyi_integration
    try:
        from perfect_tongyi_integration import TongyiQianwenLLM
        results['perfect_integration'] = True
        print_test_result("perfect_tongyi_integration导入", True)
    except Exception as e:
        results['perfect_integration'] = False
        print_test_result("perfect_tongyi_integration导入", False, str(e))
    
    # 测试working_tongyi_integration
    try:
        import working_tongyi_integration
        results['working_integration'] = True
        print_test_result("working_tongyi_integration导入", True)
    except Exception as e:
        results['working_integration'] = False
        print_test_result("working_tongyi_integration导入", False, str(e))
    
    return results

def test_api_key_configuration():
    """测试API密钥配置"""
    print_section("API密钥配置测试")
    
    # 检查环境变量
    dashscope_key = os.getenv('DASHSCOPE_API_KEY')
    openai_key = os.getenv('OPENAI_API_KEY')
    
    results = {}
    
    if dashscope_key:
        results['dashscope'] = True
        print_test_result("DASHSCOPE_API_KEY", True, f"已配置 ({dashscope_key[:10]}...)")
    else:
        results['dashscope'] = False
        print_test_result("DASHSCOPE_API_KEY", False, "未配置")
    
    if openai_key:
        results['openai'] = True
        print_test_result("OPENAI_API_KEY", True, f"已配置 ({openai_key[:10]}...)")
    else:
        results['openai'] = False
        print_test_result("OPENAI_API_KEY", False, "未配置")
    
    return results

def test_analysis_files_import():
    """测试分析功能文件导入"""
    print_section("分析功能文件导入测试")
    
    analysis_files = [
        'intent_recognition_analysis',
        'table_generation_analysis', 
        'chart_generation_analysis',
        'conversational_analysis',
        'data_volume_analysis'
    ]
    
    results = {}
    
    for file in analysis_files:
        try:
            __import__(file)
            results[file] = True
            print_test_result(f"{file}导入", True)
        except Exception as e:
            results[file] = False
            print_test_result(f"{file}导入", False, str(e))
    
    return results

def generate_test_report(all_results):
    """生成测试报告"""
    print_section("测试报告生成")
    
    total_tests = 0
    passed_tests = 0
    
    for category, results in all_results.items():
        if isinstance(results, dict):
            for test, result in results.items():
                total_tests += 1
                if result:
                    passed_tests += 1
        elif isinstance(results, bool):
            total_tests += 1
            if results:
                passed_tests += 1
    
    success_rate = (passed_tests / total_tests) * 100 if total_tests > 0 else 0
    
    print(f"\n📊 测试统计:")
    print(f"   总测试数: {total_tests}")
    print(f"   通过测试: {passed_tests}")
    print(f"   失败测试: {total_tests - passed_tests}")
    print(f"   成功率: {success_rate:.1f}%")
    
    # 判断整体状态
    if success_rate >= 90:
        status = "🎉 优秀"
        color = "绿色"
    elif success_rate >= 70:
        status = "✅ 良好"  
        color = "黄色"
    else:
        status = "⚠️ 需要关注"
        color = "红色"
    
    print(f"\n🎯 整体评估: {status} ({color})")
    
    return {
        'total_tests': total_tests,
        'passed_tests': passed_tests,
        'success_rate': success_rate,
        'status': status
    }

def main():
    """主测试函数"""
    print("🚀 项目功能测试开始")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    all_results = {}
    
    # 1. 基础导入测试
    all_results['basic_imports'] = test_basic_imports()
    
    # 2. 核心文件存在性测试
    all_results['core_files'] = test_core_files_exist()
    
    # 3. 数据创建测试
    data_success, test_df = test_data_creation()
    all_results['data_creation'] = data_success
    
    # 4. 通义千问集成导入测试
    all_results['tongyi_integration'] = test_tongyi_integration_import()
    
    # 5. API密钥配置测试
    all_results['api_keys'] = test_api_key_configuration()
    
    # 6. 分析功能文件导入测试
    all_results['analysis_files'] = test_analysis_files_import()
    
    # 7. 生成测试报告
    report = generate_test_report(all_results)
    
    print_section("测试完成")
    print("✅ 功能测试已完成")
    print("\n📋 下一步建议:")
    
    if report['success_rate'] >= 90:
        print("- 项目功能完整，可以正常使用")
        print("- 建议配置API密钥以启用完整功能")
        print("- 可以开始使用核心功能进行数据分析")
    elif report['success_rate'] >= 70:
        print("- 大部分功能正常，但有一些问题需要解决")
        print("- 检查失败的测试项目并进行修复")
        print("- 确保所有依赖都正确安装")
    else:
        print("- 存在较多问题，需要进一步检查")
        print("- 建议重新安装依赖或检查环境配置")
        print("- 查看具体错误信息进行排查")
    
    return all_results

if __name__ == "__main__":
    try:
        results = main()
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        traceback.print_exc()
