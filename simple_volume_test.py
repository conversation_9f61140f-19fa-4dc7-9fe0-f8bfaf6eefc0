#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PandasAI V2 数据量限制简化测试
"""

import os
import pandas as pd
import numpy as np
from dotenv import load_dotenv
import time
import sys

load_dotenv()

def test_data_sizes():
    """测试不同数据规模"""
    print("📊 PandasAI V2 数据量限制测试")
    print("=" * 50)
    
    # 测试不同规模的数据集
    test_cases = [
        {"name": "小数据集", "rows": 100, "cols": 5},
        {"name": "中等数据集", "rows": 1000, "cols": 10},
        {"name": "大数据集", "rows": 10000, "cols": 15},
        {"name": "超大数据集", "rows": 50000, "cols": 20}
    ]
    
    results = []
    
    for case in test_cases:
        print(f"\n🔍 测试 {case['name']}: {case['rows']:,}行 × {case['cols']}列")
        
        try:
            # 创建测试数据
            start_time = time.time()
            
            data = {}
            for i in range(case['cols']):
                if i == 0:
                    data[f'id'] = range(1, case['rows'] + 1)
                elif i == 1:
                    data[f'name'] = [f'用户{j}' for j in range(1, case['rows'] + 1)]
                elif i % 2 == 0:
                    data[f'num_col_{i}'] = np.random.randint(1, 1000, case['rows'])
                else:
                    data[f'cat_col_{i}'] = np.random.choice(['A', 'B', 'C', 'D'], case['rows'])
            
            df = pd.DataFrame(data)
            create_time = time.time() - start_time
            
            # 计算内存使用
            memory_mb = df.memory_usage(deep=True).sum() / 1024 / 1024
            
            # 测试基本操作
            start_time = time.time()
            
            # 基本统计
            desc = df.describe()
            
            # 数据筛选
            if 'num_col_2' in df.columns:
                filtered = df[df['num_col_2'] > 500]
            
            # 分组统计
            if 'cat_col_3' in df.columns:
                grouped = df.groupby('cat_col_3').size()
            
            operation_time = time.time() - start_time
            
            result = {
                'name': case['name'],
                'rows': case['rows'],
                'cols': case['cols'],
                'memory_mb': memory_mb,
                'create_time': create_time,
                'operation_time': operation_time,
                'success': True
            }
            
            print(f"   ✅ 成功")
            print(f"   💾 内存使用: {memory_mb:.2f} MB")
            print(f"   ⏱️  创建时间: {create_time:.2f}秒")
            print(f"   🔄 操作时间: {operation_time:.2f}秒")
            
        except Exception as e:
            result = {
                'name': case['name'],
                'rows': case['rows'],
                'cols': case['cols'],
                'success': False,
                'error': str(e)
            }
            print(f"   ❌ 失败: {e}")
        
        results.append(result)
    
    return results

def test_string_length_limits():
    """测试字符串长度限制"""
    print("\n🔍 测试数据字符串长度限制")
    print("=" * 50)
    
    # 创建不同大小的DataFrame
    sizes = [100, 1000, 5000, 10000]
    
    for size in sizes:
        print(f"\n测试 {size} 行数据的字符串长度:")
        
        # 创建测试数据
        df = pd.DataFrame({
            'id': range(1, size + 1),
            'name': [f'用户{i}' for i in range(1, size + 1)],
            'value': np.random.randint(1, 1000, size)
        })
        
        # 转换为字符串
        df_string = df.to_string()
        string_length = len(df_string)
        
        print(f"   📏 字符串长度: {string_length:,} 字符")
        print(f"   📊 数据大小: {string_length / 1024:.1f} KB")
        
        # 估算Token数量 (粗略估算: 1 token ≈ 4 字符)
        estimated_tokens = string_length // 4
        print(f"   🎯 估算Token数: {estimated_tokens:,}")
        
        if estimated_tokens > 100000:  # 通义千问的大致限制
            print(f"   ⚠️  可能超出API Token限制")
        else:
            print(f"   ✅ 在API Token限制内")

def analyze_limitations():
    """分析限制因素"""
    print("\n📊 PandasAI V2 数据量限制分析")
    print("=" * 50)
    
    print("""
🎯 主要限制因素:

1. 💾 系统内存限制
   - pandas DataFrame需要加载到内存
   - SmartDataframe会创建副本
   - 建议: 数据集 < 可用内存的1/3

2. 🔗 API Token限制
   - 通义千问单次请求限制: ~100K tokens
   - 1 token ≈ 4个字符 (中文)
   - 大数据集的to_string()可能超限

3. ⏱️  处理时间限制
   - LLM响应时间: 1-30秒
   - 网络超时设置: 通常30-60秒
   - 复杂查询需要更长时间

4. 🧠 LLM理解限制
   - 过多数据可能影响理解准确性
   - 建议传递数据摘要而非完整数据
   - 复杂结构可能降低分析质量

🚀 推荐的数据量上限:

📈 按使用场景分类:
- 🔬 数据探索: < 1,000行 (最佳体验)
- 📊 常规分析: < 10,000行 (良好性能)
- 📈 大数据分析: < 100,000行 (需要优化)
- 🏭 企业级应用: > 100,000行 (需要分块处理)

💡 优化策略:
1. 数据采样: 使用代表性样本
2. 分块处理: 将大数据集分割处理
3. 预处理: 清理和压缩数据
4. 缓存结果: 避免重复计算
5. 摘要传递: 只传递数据结构信息
""")

def provide_best_practices():
    """提供最佳实践建议"""
    print("\n💡 大数据量处理最佳实践")
    print("=" * 50)
    
    print("""
🎯 针对不同数据量的处理策略:

1. 小数据集 (< 1,000行)
   ✅ 直接使用SmartDataframe
   ✅ 完整数据传递给LLM
   ✅ 实时交互分析

2. 中等数据集 (1,000 - 10,000行)
   ✅ 正常使用SmartDataframe
   ⚠️  考虑数据摘要传递
   ✅ 批量查询优化

3. 大数据集 (10,000 - 100,000行)
   ⚠️  使用数据采样策略
   ⚠️  传递数据结构摘要
   ⚠️  分批处理复杂查询

4. 超大数据集 (> 100,000行)
   🔄 必须使用分块处理
   🔄 实现自定义数据管理
   🔄 考虑数据库集成

🛠️  实用工具函数:

```python
def optimize_for_pandasai(df, max_rows=10000):
    '''优化DataFrame用于PandasAI'''
    if len(df) <= max_rows:
        return df
    else:
        # 返回代表性样本
        return df.sample(n=max_rows)

def get_data_summary(df):
    '''获取数据摘要'''
    return {
        'shape': df.shape,
        'columns': list(df.columns),
        'dtypes': df.dtypes.to_dict(),
        'sample': df.head(5).to_dict(),
        'stats': df.describe().to_dict()
    }
```
""")

def main():
    """主函数"""
    print("🎯 PandasAI V2 数据量限制分析")
    print("=" * 60)
    
    # 1. 测试不同数据规模
    results = test_data_sizes()
    
    # 2. 测试字符串长度限制
    test_string_length_limits()
    
    # 3. 分析限制因素
    analyze_limitations()
    
    # 4. 最佳实践建议
    provide_best_practices()
    
    # 5. 总结报告
    print("\n" + "=" * 60)
    print("📊 测试结果总结:")
    
    for result in results:
        if result['success']:
            print(f"✅ {result['name']:12} - {result['rows']:>6,}行 - {result['memory_mb']:>6.1f}MB")
        else:
            print(f"❌ {result['name']:12} - {result['rows']:>6,}行 - 失败")
    
    print(f"\n🎯 建议:")
    print(f"- 最佳体验: < 1,000行")
    print(f"- 良好性能: < 10,000行")
    print(f"- 需要优化: < 100,000行")
    print(f"- 分块处理: > 100,000行")

if __name__ == "__main__":
    main()
