#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据处理能力测试
测试项目处理不同数据量和复杂查询的能力
"""

import pandas as pd
import numpy as np
from perfect_tongyi_integration import TongyiQianwenLLM
import time
import os

def create_small_dataset():
    """创建小型数据集"""
    data = {
        '产品': ['iPhone', 'iPad', 'MacBook', 'AirPods', 'Apple Watch'],
        '销量': [1000, 800, 500, 1200, 900],
        '价格': [6999, 4599, 14999, 1899, 3199],
        '类别': ['手机', '平板', '笔记本', '配件', '配件'],
        '月份': ['2024-01', '2024-01', '2024-01', '2024-02', '2024-02']
    }
    return pd.DataFrame(data)

def create_medium_dataset():
    """创建中型数据集"""
    np.random.seed(42)
    
    products = ['iPhone', 'iPad', 'MacBook', 'AirPods', 'Apple Watch', 'Mac Mini', 'iMac']
    categories = ['手机', '平板', '笔记本', '配件', '配件', '台式机', '台式机']
    months = ['2024-01', '2024-02', '2024-03', '2024-04', '2024-05', '2024-06']
    
    data = []
    for month in months:
        for i, product in enumerate(products):
            data.append({
                '产品': product,
                '销量': np.random.randint(100, 2000),
                '价格': np.random.randint(1000, 20000),
                '类别': categories[i],
                '月份': month,
                '成本': np.random.randint(500, 15000)
            })
    
    return pd.DataFrame(data)

def test_basic_queries(df, dataset_name):
    """测试基本查询"""
    print(f"\n🧪 测试{dataset_name}基本查询")
    print("="*40)
    
    if not os.getenv('DASHSCOPE_API_KEY'):
        print("❌ 未配置API密钥，跳过LLM测试")
        return False
    
    llm = TongyiQianwenLLM()
    
    queries = [
        "计算总销量",
        "找出销量最高的产品",
        "计算平均价格"
    ]
    
    success_count = 0
    
    for query in queries:
        print(f"\n🔍 查询: {query}")
        try:
            start_time = time.time()
            code = llm.call(query, df.head(10).to_string())  # 只传递前10行避免token限制
            end_time = time.time()
            
            print(f"📝 生成代码: {code.strip()}")
            exec(code)
            print(f"⏱️ 响应时间: {end_time - start_time:.2f}秒")
            print("✅ 查询成功")
            success_count += 1
            
        except Exception as e:
            print(f"❌ 查询失败: {e}")
    
    success_rate = (success_count / len(queries)) * 100
    print(f"\n📊 {dataset_name}查询成功率: {success_count}/{len(queries)} ({success_rate:.1f}%)")
    
    return success_count > 0

def test_complex_queries(df, dataset_name):
    """测试复杂查询"""
    print(f"\n🧪 测试{dataset_name}复杂查询")
    print("="*40)
    
    if not os.getenv('DASHSCOPE_API_KEY'):
        print("❌ 未配置API密钥，跳过复杂查询测试")
        return False
    
    llm = TongyiQianwenLLM()
    
    # 根据数据集选择合适的复杂查询
    if '成本' in df.columns:
        queries = [
            "计算每个类别的平均利润率",
            "找出利润最高的月份"
        ]
    else:
        queries = [
            "按类别统计总销量",
            "计算每个月份的销售额"
        ]
    
    success_count = 0
    
    for query in queries:
        print(f"\n🔍 复杂查询: {query}")
        try:
            start_time = time.time()
            # 对于复杂查询，传递数据结构信息而不是完整数据
            data_info = f"数据形状: {df.shape}\n列名: {list(df.columns)}\n数据类型:\n{df.dtypes.to_string()}"
            code = llm.call(query, data_info)
            end_time = time.time()
            
            print(f"📝 生成代码: {code.strip()}")
            exec(code)
            print(f"⏱️ 响应时间: {end_time - start_time:.2f}秒")
            print("✅ 复杂查询成功")
            success_count += 1
            
        except Exception as e:
            print(f"❌ 复杂查询失败: {e}")
    
    success_rate = (success_count / len(queries)) * 100
    print(f"\n📊 {dataset_name}复杂查询成功率: {success_count}/{len(queries)} ({success_rate:.1f}%)")
    
    return success_count > 0

def test_pandas_operations(df, dataset_name):
    """测试pandas基本操作"""
    print(f"\n🧪 测试{dataset_name}Pandas操作")
    print("="*40)
    
    try:
        # 基本统计
        print("📊 基本统计:")
        print(f"   数据形状: {df.shape}")
        print(f"   数值列统计:")
        numeric_cols = df.select_dtypes(include=[np.number]).columns
        for col in numeric_cols:
            print(f"   {col}: 平均值={df[col].mean():.2f}, 总和={df[col].sum()}")
        
        # 分组统计
        if '类别' in df.columns:
            print("\n📈 按类别分组统计:")
            category_stats = df.groupby('类别').agg({
                '销量': 'sum',
                '价格': 'mean'
            }).round(2)
            print(category_stats)
        
        # 排序操作
        print("\n🔝 销量前3名:")
        top_products = df.nlargest(3, '销量')[['产品', '销量', '价格']]
        print(top_products)
        
        print("✅ Pandas操作测试成功")
        return True
        
    except Exception as e:
        print(f"❌ Pandas操作测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 数据处理能力测试开始")
    print("="*60)
    
    # 创建测试数据集
    print("📊 创建测试数据集...")
    small_df = create_small_dataset()
    medium_df = create_medium_dataset()
    
    print(f"✅ 小型数据集: {small_df.shape}")
    print(f"✅ 中型数据集: {medium_df.shape}")
    
    # 测试结果统计
    results = {}
    
    # 测试小型数据集
    results['small_pandas'] = test_pandas_operations(small_df, "小型数据集")
    results['small_basic'] = test_basic_queries(small_df, "小型数据集")
    results['small_complex'] = test_complex_queries(small_df, "小型数据集")
    
    # 测试中型数据集
    results['medium_pandas'] = test_pandas_operations(medium_df, "中型数据集")
    results['medium_basic'] = test_basic_queries(medium_df, "中型数据集")
    results['medium_complex'] = test_complex_queries(medium_df, "中型数据集")
    
    # 生成总结报告
    print("\n" + "="*60)
    print("📋 数据处理能力测试总结")
    print("="*60)
    
    total_tests = len(results)
    passed_tests = sum(1 for result in results.values() if result)
    success_rate = (passed_tests / total_tests) * 100
    
    print(f"总测试项: {total_tests}")
    print(f"通过测试: {passed_tests}")
    print(f"成功率: {success_rate:.1f}%")
    
    print("\n📊 详细结果:")
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
    
    if success_rate >= 80:
        print("\n🎉 数据处理能力优秀！")
        print("✅ 项目可以处理不同规模的数据")
        print("✅ 基本和复杂查询都能正常工作")
    elif success_rate >= 60:
        print("\n✅ 数据处理能力良好")
        print("⚠️ 部分功能可能需要优化")
    else:
        print("\n⚠️ 数据处理能力需要改进")
        print("建议检查API配置和网络连接")
    
    return results

if __name__ == "__main__":
    main()
