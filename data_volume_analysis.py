#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PandasAI V2 数据量限制分析
基于通义千问集成的大数据量测试
"""

import os
import pandas as pd
import numpy as np
from dotenv import load_dotenv
from pandasai.llm.base import LLM
from pandasai import SmartDataframe
import requests
import re
import time
import psutil
import sys

load_dotenv()

class DataVolumeLLM(LLM):
    """专门用于大数据量测试的LLM类"""
    
    def __init__(self, model="qwen-plus"):
        self.api_key = os.getenv('DASHSCOPE_API_KEY')
        self.model = model
        self.base_url = "https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions"
        
    def call(self, instruction, value):
        """优化的大数据量调用"""
        # 对于大数据量，只传递数据结构信息而不是完整数据
        data_info = self.get_data_summary(value)
        
        prompt = f"""你是数据分析专家，正在处理大数据集。

数据结构信息:
{data_info}

用户查询: {instruction}

要求:
1. 生成高效的Python代码
2. 使用变量名df表示DataFrame
3. 优化内存使用
4. 包含适当的输出语句
5. 考虑大数据集的性能优化

Python代码:"""

        headers = {"Authorization": f"Bearer {self.api_key}", "Content-Type": "application/json"}
        data = {"model": self.model, "messages": [{"role": "user", "content": prompt}], "temperature": 0.1}
        
        try:
            response = requests.post(self.base_url, headers=headers, json=data, timeout=60)
            if response.status_code == 200:
                code = response.json()['choices'][0]['message']['content']
                return self.clean_code(code)
            return "print('API调用失败')"
        except Exception as e:
            return f"print('API异常: {e}')"
    
    def get_data_summary(self, value):
        """获取数据摘要信息，避免传递完整大数据集"""
        if len(value) > 10000:  # 如果数据太大，只传递摘要
            lines = value.split('\n')
            summary = f"""
数据行数: 约{len(lines)}行
数据样本 (前5行):
{chr(10).join(lines[:6])}
...
数据样本 (后5行):
{chr(10).join(lines[-5:])}
"""
            return summary
        return value
    
    def clean_code(self, code):
        """清理生成的代码"""
        code = re.sub(r'```python\n?', '', code)
        code = re.sub(r'```\n?', '', code)
        
        lines = code.split('\n')
        clean_lines = []
        
        for line in lines:
            line = line.strip()
            if line and not any(char in line for char in '。，：；！？""''（）【】'):
                if not (line and ord(line[0]) > 127 and not any(op in line for op in ['=', '(', '[', 'df', 'print'])):
                    clean_lines.append(line)
        
        return '\n'.join(clean_lines).strip()
    
    @property
    def type(self):
        return "data_volume_llm"

def create_test_datasets():
    """创建不同规模的测试数据集"""
    print("📊 创建不同规模的测试数据集")
    print("=" * 50)
    
    datasets = {}
    
    # 小数据集 (100行)
    print("创建小数据集 (100行)...")
    small_data = {
        'id': range(1, 101),
        'name': [f'用户{i}' for i in range(1, 101)],
        'age': np.random.randint(18, 65, 100),
        'salary': np.random.randint(3000, 20000, 100),
        'department': np.random.choice(['技术部', '销售部', '市场部', '人事部'], 100),
        'score': np.random.uniform(60, 100, 100).round(2)
    }
    datasets['small'] = pd.DataFrame(small_data)
    
    # 中等数据集 (10,000行)
    print("创建中等数据集 (10,000行)...")
    medium_data = {
        'id': range(1, 10001),
        'name': [f'用户{i}' for i in range(1, 10001)],
        'age': np.random.randint(18, 65, 10000),
        'salary': np.random.randint(3000, 50000, 10000),
        'department': np.random.choice(['技术部', '销售部', '市场部', '人事部', '财务部'], 10000),
        'score': np.random.uniform(60, 100, 10000).round(2),
        'city': np.random.choice(['北京', '上海', '广州', '深圳', '杭州'], 10000)
    }
    datasets['medium'] = pd.DataFrame(medium_data)
    
    # 大数据集 (100,000行)
    print("创建大数据集 (100,000行)...")
    large_data = {
        'id': range(1, 100001),
        'name': [f'用户{i}' for i in range(1, 100001)],
        'age': np.random.randint(18, 65, 100000),
        'salary': np.random.randint(3000, 100000, 100000),
        'department': np.random.choice(['技术部', '销售部', '市场部', '人事部', '财务部', '运营部'], 100000),
        'score': np.random.uniform(60, 100, 100000).round(2),
        'city': np.random.choice(['北京', '上海', '广州', '深圳', '杭州', '成都', '武汉', '西安'], 100000),
        'join_date': pd.date_range('2020-01-01', periods=100000, freq='H')[:100000]
    }
    datasets['large'] = pd.DataFrame(large_data)
    
    # 超大数据集 (500,000行) - 可选
    print("创建超大数据集 (500,000行)...")
    try:
        xlarge_data = {
            'id': range(1, 500001),
            'value': np.random.randn(500000),
            'category': np.random.choice(['A', 'B', 'C', 'D', 'E'], 500000),
            'timestamp': pd.date_range('2020-01-01', periods=500000, freq='min')[:500000]
        }
        datasets['xlarge'] = pd.DataFrame(xlarge_data)
        print("✅ 超大数据集创建成功")
    except MemoryError:
        print("⚠️  内存不足，跳过超大数据集")
        datasets['xlarge'] = None
    
    return datasets

def test_data_volume_limits(datasets):
    """测试不同数据量的处理能力"""
    print("\n🔍 测试不同数据量的处理能力")
    print("=" * 50)
    
    llm = DataVolumeLLM()
    results = {}
    
    for size_name, df in datasets.items():
        if df is None:
            continue
            
        print(f"\n📊 测试 {size_name} 数据集:")
        print(f"   数据形状: {df.shape}")
        print(f"   内存使用: {df.memory_usage(deep=True).sum() / 1024 / 1024:.2f} MB")
        
        # 记录开始时间和内存
        start_time = time.time()
        start_memory = psutil.Process().memory_info().rss / 1024 / 1024
        
        try:
            # 创建SmartDataframe
            smart_df = SmartDataframe(df, config={
                "llm": llm,
                "verbose": False,
                "conversational": False
            })
            
            # 执行简单查询
            query = "计算平均工资" if 'salary' in df.columns else "计算数据总行数"
            result = smart_df.chat(query)
            
            # 记录结束时间和内存
            end_time = time.time()
            end_memory = psutil.Process().memory_info().rss / 1024 / 1024
            
            results[size_name] = {
                'success': True,
                'rows': df.shape[0],
                'columns': df.shape[1],
                'memory_mb': df.memory_usage(deep=True).sum() / 1024 / 1024,
                'processing_time': end_time - start_time,
                'memory_increase': end_memory - start_memory,
                'result': str(result)[:100] + '...' if len(str(result)) > 100 else str(result)
            }
            
            print(f"   ✅ 处理成功")
            print(f"   ⏱️  处理时间: {end_time - start_time:.2f}秒")
            print(f"   💾 内存增加: {end_memory - start_memory:.2f}MB")
            print(f"   📝 查询结果: {results[size_name]['result']}")
            
        except Exception as e:
            results[size_name] = {
                'success': False,
                'rows': df.shape[0],
                'columns': df.shape[1],
                'memory_mb': df.memory_usage(deep=True).sum() / 1024 / 1024,
                'error': str(e)
            }
            print(f"   ❌ 处理失败: {e}")
    
    return results

def test_api_token_limits():
    """测试API Token限制"""
    print("\n🔍 测试API Token限制")
    print("=" * 50)
    
    # 创建不同大小的数据字符串
    test_sizes = [1000, 5000, 10000, 50000, 100000]
    
    for size in test_sizes:
        print(f"\n测试数据字符串长度: {size}")
        
        # 创建测试数据字符串
        test_data = "测试数据\n" * (size // 10)
        
        llm = DataVolumeLLM()
        
        start_time = time.time()
        try:
            result = llm.call("分析这些数据", test_data)
            end_time = time.time()
            
            print(f"   ✅ API调用成功")
            print(f"   ⏱️  响应时间: {end_time - start_time:.2f}秒")
            print(f"   📝 响应长度: {len(result)}字符")
            
        except Exception as e:
            print(f"   ❌ API调用失败: {e}")

def analyze_performance_bottlenecks():
    """分析性能瓶颈"""
    print("\n🔍 性能瓶颈分析")
    print("=" * 50)
    
    print("""
🎯 PandasAI V2 数据量限制分析:

1. 📊 DataFrame大小限制
   - 主要受系统内存限制
   - pandas本身支持大数据集
   - 建议单个DataFrame < 1GB内存

2. 🔗 API调用限制
   - 通义千问API有Token限制
   - 单次请求建议 < 100K tokens
   - 大数据集需要数据摘要策略

3. ⏱️  处理时间限制
   - LLM响应时间: 1-30秒
   - 数据处理时间随数据量线性增长
   - 复杂查询可能需要更长时间

4. 💾 内存使用限制
   - SmartDataframe会复制DataFrame
   - 建议可用内存 > 数据集大小 × 3
   - 大数据集考虑分块处理

5. 🚀 优化策略
   - 数据预处理和清洗
   - 使用数据采样
   - 分批处理大数据集
   - 缓存常用查询结果
""")

def provide_optimization_recommendations():
    """提供优化建议"""
    print("\n💡 大数据量优化建议")
    print("=" * 50)
    
    optimization_code = '''
# 大数据量优化策略示例

class OptimizedDataAnalysis:
    """优化的大数据分析类"""
    
    def __init__(self, df, chunk_size=10000):
        self.df = df
        self.chunk_size = chunk_size
        self.llm = DataVolumeLLM()
    
    def analyze_large_dataset(self, query):
        """分析大数据集"""
        if len(self.df) <= self.chunk_size:
            # 小数据集直接处理
            return self.direct_analysis(query)
        else:
            # 大数据集分块处理
            return self.chunked_analysis(query)
    
    def direct_analysis(self, query):
        """直接分析"""
        smart_df = SmartDataframe(self.df, config={"llm": self.llm})
        return smart_df.chat(query)
    
    def chunked_analysis(self, query):
        """分块分析"""
        # 1. 数据采样
        sample_df = self.df.sample(n=min(1000, len(self.df)))
        
        # 2. 基于样本生成分析代码
        smart_sample = SmartDataframe(sample_df, config={"llm": self.llm})
        code = smart_sample.chat(f"生成分析代码: {query}")
        
        # 3. 在完整数据集上执行代码
        try:
            exec(code, {'df': self.df})
        except Exception as e:
            return f"分析失败: {e}"
    
    def get_data_summary(self):
        """获取数据摘要"""
        return {
            'shape': self.df.shape,
            'memory_usage': self.df.memory_usage(deep=True).sum(),
            'dtypes': self.df.dtypes.to_dict(),
            'sample': self.df.head().to_dict()
        }

# 使用示例
# analyzer = OptimizedDataAnalysis(large_df)
# result = analyzer.analyze_large_dataset("计算各部门平均工资")
'''
    
    print("📝 优化代码示例:")
    print(optimization_code)

def main():
    """主函数"""
    print("🎯 PandasAI V2 数据量限制深度分析")
    print("=" * 60)
    
    # 检查API密钥
    if not os.getenv('DASHSCOPE_API_KEY'):
        print("❌ 未找到DASHSCOPE_API_KEY环境变量")
        return
    
    print(f"✅ API密钥已配置")
    print(f"💻 系统内存: {psutil.virtual_memory().total / 1024 / 1024 / 1024:.1f} GB")
    print(f"🐍 Python版本: {sys.version}")
    
    # 1. 创建测试数据集
    datasets = create_test_datasets()
    
    # 2. 测试数据量限制
    results = test_data_volume_limits(datasets)
    
    # 3. 测试API限制
    test_api_token_limits()
    
    # 4. 性能分析
    analyze_performance_bottlenecks()
    
    # 5. 优化建议
    provide_optimization_recommendations()
    
    # 6. 总结报告
    print("\n" + "=" * 60)
    print("📊 数据量限制测试总结:")
    
    for size_name, result in results.items():
        if result['success']:
            print(f"✅ {size_name:8} - {result['rows']:>8,}行 - {result['memory_mb']:>6.1f}MB - {result['processing_time']:>5.1f}s")
        else:
            print(f"❌ {size_name:8} - {result['rows']:>8,}行 - 处理失败")
    
    print("\n🎯 建议的数据量上限:")
    print("- 小型项目: < 10,000行")
    print("- 中型项目: < 100,000行") 
    print("- 大型项目: < 500,000行 (需要优化)")
    print("- 超大项目: 需要分块处理策略")

if __name__ == "__main__":
    main()
